/**
 * Map Operations Module
 * Handles all map-related functionality for journey and event maps
 */

// Global map instances
let journeyMap = null;
let eventMaps = {};

/**
 * Safely parse JSON data with error handling
 * @param {string} jsonString - JSON string to parse
 * @param {*} fallback - Fallback value if parsing fails
 * @returns {*} Parsed JSON or fallback value
 */
function safeJsonParse(jsonString, fallback = []) {
    try {
        if (!jsonString || jsonString.trim() === '') {
            return fallback;
        }
        return JSON.parse(jsonString);
    } catch (error) {
        console.error('JSON parsing error:', error);
        console.error('Problematic JSON string:', jsonString);
        return fallback;
    }
}

/**
 * Get locations data from page data attributes
 * @returns {Array} Array of location objects
 */
function getLocationsData() {
    const pageData = document.getElementById('pageData');
    if (!pageData) {
        console.warn('Page data element not found');
        return [];
    }
    
    const locationsData = pageData.dataset.locations;
    return safeJsonParse(locationsData, []);
}

/**
 * Create a red marker icon for maps
 * @returns {L.Icon} Leaflet icon object
 */
function createRedIcon() {
    return L.icon({
        iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41]
    });
}

/**
 * Initialize journey map
 */
function initializeJourneyMap() {
    const mapElement = document.getElementById('journeyMap');
    if (!mapElement || typeof L === 'undefined') {
        console.warn('Journey map element not found or Leaflet not loaded');
        return;
    }

    const journeyLocations = getLocationsData();
    if (!journeyLocations || journeyLocations.length === 0) {
        console.info('No locations data available for journey map');
        return;
    }

    try {
        // Sort locations by event start time
        const sortedLocations = journeyLocations.sort((a, b) =>
            new Date(a.event_start_datetime) - new Date(b.event_start_datetime)
        );

        const firstLocation = sortedLocations[0];
        const firstLat = parseFloat(firstLocation.latitude);
        const firstLng = parseFloat(firstLocation.longitude);

        if (isNaN(firstLat) || isNaN(firstLng)) {
            console.error('Invalid coordinates for first location');
            return;
        }

        // Initialize map
        journeyMap = L.map('journeyMap').setView([firstLat, firstLng], 10);
        L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
        }).addTo(journeyMap);

        const redIcon = createRedIcon();
        const pathPoints = [];

        // Add markers and collect path points
        sortedLocations.forEach((location, index) => {
            const lat = parseFloat(location.latitude);
            const lng = parseFloat(location.longitude);

            if (!isNaN(lat) && !isNaN(lng)) {
                pathPoints.push([lat, lng]);

                const marker = L.marker([lat, lng], { icon: redIcon }).addTo(journeyMap);
                marker.bindPopup(`
                    <div class="event-popup">
                        <h6>${location.event_title || 'Event'}</h6>
                        <p><strong>Location:</strong> ${location.location_name || 'Unknown'}</p>
                        <p><strong>Date:</strong> ${new Date(location.event_start_datetime).toLocaleDateString()}</p>
                    </div>
                `);
            }
        });

        // Draw path if we have multiple points
        if (pathPoints.length > 1) {
            L.polyline(pathPoints, {
                color: 'red',
                weight: 3,
                opacity: 0.7,
                smoothFactor: 1
            }).addTo(journeyMap);

            // Fit bounds to show all points
            const bounds = L.latLngBounds(pathPoints);
            journeyMap.fitBounds(bounds, { padding: [30, 30] });
        }
    } catch (error) {
        console.error('Error initializing journey map:', error);
    }
}

/**
 * Toggle event map display
 * @param {string} eventId - Event ID
 * @param {string|null} latitude - Latitude coordinate
 * @param {string|null} longitude - Longitude coordinate
 * @param {string} locationName - Location name
 */
function toggleEventMap(eventId, latitude, longitude, locationName) {
    const mapContainer = document.getElementById(`eventMap${eventId}`);
    const toggleIcon = document.getElementById(`mapToggle${eventId}`);

    if (!mapContainer) {
        console.warn(`Event map container not found for event ${eventId}`);
        return;
    }

    if (mapContainer.style.display === 'none') {
        mapContainer.style.display = 'block';
        if (toggleIcon) toggleIcon.classList.add('rotate-180');

        // Initialize map if not already done
        if (!eventMaps[eventId] && latitude && longitude) {
            setTimeout(() => initializeEventMap(eventId, latitude, longitude, locationName), 100);
        }
    } else {
        mapContainer.style.display = 'none';
        if (toggleIcon) toggleIcon.classList.remove('rotate-180');
    }
}

/**
 * Initialize individual event map
 * @param {string} eventId - Event ID
 * @param {string} latitude - Latitude coordinate
 * @param {string} longitude - Longitude coordinate
 * @param {string} locationName - Location name
 */
function initializeEventMap(eventId, latitude, longitude, locationName) {
    const mapElement = document.getElementById(`inlineMap${eventId}`);
    if (!mapElement || typeof L === 'undefined') {
        console.warn(`Event map element not found for event ${eventId} or Leaflet not loaded`);
        return;
    }

    try {
        const lat = parseFloat(latitude);
        const lng = parseFloat(longitude);

        if (isNaN(lat) || isNaN(lng)) {
            console.error(`Invalid coordinates for event ${eventId}: lat=${latitude}, lng=${longitude}`);
            return;
        }

        const map = L.map(`inlineMap${eventId}`).setView([lat, lng], 15);
        L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
        }).addTo(map);

        const redIcon = createRedIcon();
        L.marker([lat, lng], { icon: redIcon })
            .addTo(map)
            .bindPopup(`<div class="location-popup"><h6>${locationName || 'Location'}</h6></div>`)
            .openPopup();

        eventMaps[eventId] = map;
    } catch (error) {
        console.error(`Error initializing event map ${eventId}:`, error);
    }
}

/**
 * Open journey map modal
 */
function openJourneyMapModal() {
    const modal = new bootstrap.Modal(document.getElementById('fullMapModal'));
    modal.show();

    // Initialize full map after modal is shown
    setTimeout(() => {
        initializeFullJourneyMap();
    }, 300);
}

/**
 * Open event location modal
 * @param {string|null} latitude - Latitude coordinate
 * @param {string|null} longitude - Longitude coordinate
 * @param {string} locationName - Location name
 */
function openFullMap(latitude, longitude, locationName) {
    if (!latitude || !longitude) {
        console.warn('Invalid coordinates for full map');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('eventLocationModal'));
    document.getElementById('eventLocationModalLabel').textContent = locationName || 'Event Location';
    modal.show();

    // Initialize event location map after modal is shown
    setTimeout(() => {
        initializeFullEventMap(latitude, longitude, locationName);
    }, 300);
}

/**
 * Initialize full journey map in modal
 */
function initializeFullJourneyMap() {
    const mapElement = document.getElementById('fullJourneyMap');
    if (!mapElement || typeof L === 'undefined') {
        console.warn('Full journey map element not found or Leaflet not loaded');
        return;
    }

    const journeyLocations = getLocationsData();
    if (!journeyLocations || journeyLocations.length === 0) {
        console.info('No locations data available for full journey map');
        return;
    }

    try {
        // Remove existing map if any
        if (window.fullJourneyMapInstance) {
            window.fullJourneyMapInstance.remove();
        }

        // Sort locations by event start time
        const sortedLocations = journeyLocations.sort((a, b) =>
            new Date(a.event_start_datetime) - new Date(b.event_start_datetime)
        );

        const firstLocation = sortedLocations[0];
        const firstLat = parseFloat(firstLocation.latitude);
        const firstLng = parseFloat(firstLocation.longitude);

        if (isNaN(firstLat) || isNaN(firstLng)) {
            console.error('Invalid coordinates for first location in full map');
            return;
        }

        window.fullJourneyMapInstance = L.map('fullJourneyMap').setView([firstLat, firstLng], 10);
        L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
        }).addTo(window.fullJourneyMapInstance);

        const redIcon = createRedIcon();
        const pathPoints = [];

        // Add markers and collect path points
        sortedLocations.forEach((location, index) => {
            const lat = parseFloat(location.latitude);
            const lng = parseFloat(location.longitude);

            if (!isNaN(lat) && !isNaN(lng)) {
                pathPoints.push([lat, lng]);

                const marker = L.marker([lat, lng], { icon: redIcon }).addTo(window.fullJourneyMapInstance);
                marker.bindPopup(`
                    <div class="event-popup">
                        <h6>${location.event_title || 'Event'}</h6>
                        <p><strong>Location:</strong> ${location.location_name || 'Unknown'}</p>
                        <p><strong>Date:</strong> ${new Date(location.event_start_datetime).toLocaleDateString()}</p>
                    </div>
                `);
            }
        });

        // Draw path if we have multiple points
        if (pathPoints.length > 1) {
            L.polyline(pathPoints, {
                color: 'red',
                weight: 3,
                opacity: 0.7,
                smoothFactor: 1
            }).addTo(window.fullJourneyMapInstance);

            // Fit bounds to show all points
            const bounds = L.latLngBounds(pathPoints);
            window.fullJourneyMapInstance.fitBounds(bounds, { padding: [30, 30] });
        }
    } catch (error) {
        console.error('Error initializing full journey map:', error);
    }
}

/**
 * Initialize full event map in modal
 * @param {string} latitude - Latitude coordinate
 * @param {string} longitude - Longitude coordinate
 * @param {string} locationName - Location name
 */
function initializeFullEventMap(latitude, longitude, locationName) {
    const mapElement = document.getElementById('fullEventMap');
    if (!mapElement || typeof L === 'undefined') {
        console.warn('Full event map element not found or Leaflet not loaded');
        return;
    }

    try {
        // Remove existing map if any
        if (window.fullEventMapInstance) {
            window.fullEventMapInstance.remove();
        }

        const lat = parseFloat(latitude);
        const lng = parseFloat(longitude);

        if (isNaN(lat) || isNaN(lng)) {
            console.error(`Invalid coordinates for full event map: lat=${latitude}, lng=${longitude}`);
            return;
        }

        window.fullEventMapInstance = L.map('fullEventMap').setView([lat, lng], 15);
        L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
        }).addTo(window.fullEventMapInstance);

        const redIcon = createRedIcon();
        L.marker([lat, lng], { icon: redIcon })
            .addTo(window.fullEventMapInstance)
            .bindPopup(`<div class="location-popup"><h6>${locationName || 'Location'}</h6></div>`)
            .openPopup();

    } catch (error) {
        console.error('Error initializing full event map:', error);
    }
}

// Make functions globally available
window.initializeJourneyMap = initializeJourneyMap;
window.toggleEventMap = toggleEventMap;
window.openJourneyMapModal = openJourneyMapModal;
window.openFullMap = openFullMap;

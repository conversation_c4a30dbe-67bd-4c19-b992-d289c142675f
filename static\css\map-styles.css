/**
 * Map Styles Module
 * Styles for Leaflet maps and map-related components
 */

/* Journey Map Styling */
.journey-map-container {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#journeyMap {
  border-radius: 8px;
  min-height: 250px;
}

#fullJourneyMap {
  min-height: 70vh;
}

#fullEventMap {
  min-height: 50vh;
}

/* Leaflet popup styling */
.leaflet-popup-content {
  margin: 8px 12px;
}

.event-popup h6 {
  color: #495057;
  margin-bottom: 8px;
  font-weight: 600;
}

.location-popup h6 {
  color: #495057;
  margin-bottom: 8px;
  font-weight: 600;
}

.event-popup p {
  margin-bottom: 4px;
  font-size: 0.9rem;
}

.location-popup p {
  margin-bottom: 4px;
  font-size: 0.9rem;
}

/* Map loading state */
.map-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 250px;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.map-loading i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* Map error state */
.map-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #dc3545;
  text-align: center;
  padding: 1rem;
}

.map-error i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* Ensure Leaflet controls are visible */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 1px 5px rgba(0,0,0,0.4) !important;
}

.leaflet-control-zoom a {
  background-color: #fff !important;
  border-bottom: 1px solid #ccc !important;
  color: #333 !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f4f4f4 !important;
}

/* Attribution styling */
.leaflet-control-attribution {
  background: rgba(255, 255, 255, 0.8) !important;
  font-size: 11px !important;
}

/* Custom marker styling */
.custom-marker {
  background-color: #dc3545;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Map modal styling */
.modal .leaflet-container {
  height: 100% !important;
}

/* Responsive map styling */
@media (max-width: 768px) {
  .journey-map-container {
    margin: 0 -15px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  #journeyMap {
    border-radius: 0;
    min-height: 200px;
  }
  
  .map-loading,
  .map-error {
    height: 200px;
    margin: 0 -15px;
    border-radius: 0;
  }
}

/* Event location modal specific styling */
#eventLocationModal .modal-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

#eventLocationModal .modal-title {
  color: #495057;
  font-weight: 600;
}

#eventLocationModal .btn[data-action="toggle-follow"] {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

#eventLocationModal .btn[data-action="toggle-follow"] i {
  font-size: 0.875rem;
}

/* Journey map modal styling */
#fullMapModal .modal-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

#fullMapModal .modal-title {
  color: #495057;
  font-weight: 600;
}

/* Map container ensures proper sizing */
.leaflet-container {
  font-family: inherit;
}

/* Fix for map tiles loading */
.leaflet-tile-container {
  pointer-events: auto;
}

/* Ensure map is visible and properly sized */
.leaflet-map-pane {
  z-index: 1;
}

.leaflet-tile {
  pointer-events: auto;
}

/* Debug styles - remove in production */
.debug-map-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  display: none; /* Hidden by default */
}

.debug-mode .debug-map-info {
  display: block;
}

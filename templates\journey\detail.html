{% extends "base.html" %}

{% block title %}
{{ journey.title }} - Footprints
{% endblock %}

{% block head %}
<!-- Modular CSS files -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/card-layouts.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/comments.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/image-gallery.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/journey-detail.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/timeline.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/map-styles.css') }}">

<!-- Modular JavaScript utilities -->
<script src="{{ url_for('static', filename='js/journey-operations.js') }}"></script>
<script src="{{ url_for('static', filename='js/event-operations.js') }}"></script>
<script src="{{ url_for('static', filename='js/location-operations.js') }}"></script>
<script src="{{ url_for('static', filename='js/map-operations.js') }}"></script>
<script src="{{ url_for('static', filename='js/journey-detail.js') }}"></script>
{% endblock %}

{% block content %}
<!-- Data attributes for JavaScript -->
<div id="pageData"
     data-journey-user-id="{{ journey.user_id }}"
     data-session-user-id="{{ session.user_id if session.user_id else '' }}"
     data-is-staff="{{ 'true' if can_manage_content() else 'false' }}"
     data-back-url="{{ url_for('journey.get_public_journeys') }}"
     data-journey-id="{{ journey.id }}"
     data-can-edit="{{ 'true' if journey.user_id == session.user_id or can_manage_content() else 'false' }}"
     style="display: none;"></div>

<!-- Locations data in script tag to avoid HTML attribute length limits -->
<script type="application/json" id="locationsData">{{ locations | tojson | safe }}</script>

<a href="javascript:void(0)" onclick="smartBack()"
  class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
  <i class="bi bi-arrow-left me-2"></i>
  <span id="backButtonText">Back</span>
</a>
<div class="row g-4" id="journeyContainer">
  <!-- Journey details panel -->
  <div class="col-md-4">
    <div class="card shadow-sm border-0 rounded-3 h-100">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h1 class="fs-3 fw-bold mb-0">Journey Details</h1>

          <div class="d-flex align-items-center gap-2">
            <!-- Follow button (visible for non-owners) -->
            {% if session.get('user_id') and journey.user_id != session.user_id %}
            <button type="button"
                    class="btn {% if is_following_journey %}btn-primary{% else %}{% endif %} btn-sm d-flex align-items-center px-3"
                    data-action="toggle-follow"
                    data-journey-id="{{ journey.id }}">
              <i class="bi bi-heart{% if is_following_journey %}-fill{% endif %} me-1"></i>
              <span class="btn-text d-none d-sm-inline">{% if is_following_journey %}Following{% else %}Follow{% endif %}</span>
            </button>
            {% endif %}

            <!-- Journey menu -->
            {% if session.get('user_id') %}
            <div class="dropdown">
              <button class="menu-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-three-dots-vertical"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                {% if journey.user_id != session.user_id %}
                <!-- Follow button moved out of dropdown -->
                {% endif %}
              {% if journey.user_id == session.user_id or can_manage_content() %}
              <li>
                {% if journey.no_edits and journey.user_id != session.user_id %}
                <a href="#" class="dropdown-item text-muted" onclick="showProtectedJourneyMessage()"
                  style="cursor: not-allowed;">
                  <i class="bi bi-shield-lock me-2"></i>Edit Journey (Protected)
                </a>
                {% else %}
                <button type="button" class="dropdown-item"
                        data-action="edit-journey"
                        data-journey-id="{{ journey.id }}">
                  <i class="bi bi-pencil me-2"></i>Edit Journey
                </button>
                {% endif %}
              </li>
              {% if can_access_edit_history() or journey.user_id == session.get('user_id') %}
              <li>
                <a href="{{ url_for('edit_history.get_journey_edit_history', journey_id=journey.id) }}"
                  class="dropdown-item">
                  <i class="bi bi-clock-history me-2"></i>View Edit History
                </a>
              </li>
              {% endif %}
              {% if can_manage_content() and not journey.user_id == session.user_id %}
              <li>
                <button type="button" class="dropdown-item text-warning"
                        data-action="toggle-hidden"
                        data-journey-id="{{ journey.id }}">
                  <i class="bi bi-slash-circle me-2"></i>
                  <span class="btn-text">{% if not journey.is_hidden %}Hide{% else %}Unhide{% endif %} Journey</span>
                </button>
              </li>
              {% endif %}
              {% if journey.user_id == session.user_id %}
              <li>
                <hr class="dropdown-divider">
              </li>
              <li>
                <form method="post"
                  action="{{ url_for('journey.delete_' + ('admin_' if is_admin_view else '') + 'journey', journey_id=journey.id) }}"
                  id="deleteJourneyForm" class="d-inline">
                  <button type="button" class="dropdown-item text-danger"
                          data-action="delete-journey"
                          data-journey-id="{{ journey.id }}">
                    <i class="bi bi-trash me-2"></i>Delete Journey
                  </button>
                </form>
              </li>
              {% endif %}
              {% endif %}
            </ul>
            </div>
            {% endif %}
          </div>
        </div>
        <div class="mb-2">
          <span
            class="badge visibility-badge {% if journey.visibility in ('public', 'published') %}bg-success{% else %}bg-secondary{% endif %} rounded-pill py-1 px-3">
            {% if journey.visibility in ('public', 'published') %}{{ journey.visibility|capitalize }}{% else
            %}Private{% endif %}
          </span>
          {% if journey.is_hidden %}
          <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1 hidden-badge">Hidden</span>
          {% endif %}
          {% if journey.no_edits and (journey.user_id == session.user_id or can_manage_content()) %}
          <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1"
            title="This journey is protected from staff edits">
            <i class="bi bi-shield-lock me-1"></i>Protected
          </span>
          {% endif %}
        </div>

        <!-- Hidden Journey Appeal Section -->
        {% if journey.is_hidden and journey.user_id == session.user_id %}
        <div class="alert alert-warning rounded-3 mb-2" id="appealSection">
          <div class="d-flex align-items-start">
            <i class="bi bi-eye-slash fs-5 me-3 mt-1"></i>
            <div class="flex-grow-1">
              <h6 class="fw-bold mb-2">Journey Hidden by Staff</h6>
              <p class="mb-2 small">This journey has been hidden by content management staff and is not visible to other
                users.</p>

              <!-- Show rejection reason if appeal was rejected -->
              {% if appeal_status and appeal_status.status == 'rejected' and appeal_status.admin_response %}
              <div class="bg-light rounded p-3 mb-3" id="rejectionReason">
                <h6 class="small fw-bold mb-1 text-danger">
                  <i class="bi bi-x-circle me-1"></i>Appeal Rejected - Staff Response:
                </h6>
                <p class="mb-0 small">{{ appeal_status.admin_response }}</p>
              </div>
              {% endif %}

              <a href="{{ url_for('helpdesk.journey_appeal', journey_id=journey.id) }}"
                class="btn btn-sm btn-warning rounded-pill">
                <i class="bi bi-flag me-1"></i>
                {% if not appeal_status %}
                Submit Appeal
                {% elif appeal_status.status == 'rejected' %}
                Submit New Appeal
                {% elif appeal_status.status in ['new', 'open'] %}
                View Appeal Status
                {% else %}
                Appeal Decision
                {% endif %}
              </a>
            </div>
          </div>
        </div>
        {% endif %}

        <!-- Author info -->
        <div class="d-flex align-items-center mb-4">
          <div class="position-relative me-3">
            <div class="rounded-circle overflow-hidden">
              <img {% if journey.profile_image %}
                src="{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}"
                alt="{{ journey.username }}" style="width: 50px; height: 50px; object-fit: cover;"
                onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
              {% else %}
              <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                alt="{{ journey.username }}" style="width: 50px; height: 50px; object-fit: cover;">
              {% endif %}
            </div>
          </div>
        
          <div class="d-flex flex-column align-items-start">
            {% if journey.user_id != session.user_id %}
            <a href="{{ url_for('account.get_public_profile', username=journey.username) }}" 
               class="text-decoration-none username-link text-primary fw-semibold ">
              {{ journey.username }}
            </a>
            {% else %}
            <div class="fw-semibold">{{ journey.username }}</div>
            {% endif %}
            {% if journey.updated_at and journey.updated_at != journey.created_at %}
            <small class="text-muted">Updated {{ journey.updated_at.strftime('%B %d, %Y') }}</small>
            {% else %}
            <small class="text-muted">Created {{ journey.created_at.strftime('%B %d, %Y') }}</small}
            {% endif %}
          </div>
        </div>
        

        <!-- Journey info -->
        <div class="journey-info">
          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Title</h6>
            <p class="text-dark fs-4 fw-medium mb-0" id="journeyTitle">{{ journey.title }}</p>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Start Date</h6>
            <p class="mb-0" id="journeyStartDate">{{ journey.start_date.strftime('%B %d, %Y') }}</p>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Description</h6>
            <p class="mb-0" id="journeyDescription">{{ journey.description }}</p>
          </div>
          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Cover Image</h6>
            <div class="cover-image-container mb-2">
              {% if journey.cover_image %}
              <div class="cover-image position-relative" id="coverImageContainer">
                <div class="cover-image-clickable"
                  data-image-url="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
                  data-title="{{ journey.title }}"
                  onclick="showCoverImageModal(this.dataset.imageUrl, this.dataset.title)">
                  <img src="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
                    alt="Cover image" class="cover-image-img" id="coverImageImg">
                  <div class="cover-image-overlay">
                    <i class="bi bi-zoom-in"></i>
                    <span>Click to enlarge</span>
                  </div>
                </div>
                {% if journey.user_id == session.user_id or can_manage_content() %}
                <div class="cover-image-controls position-absolute bottom-0 end-0 m-2">
                  {% if journey.user_id == session.user_id %}
                  <button class="btn btn-sm btn-light rounded-pill me-2" id="changeCoverImageBtn">
                    <i class="bi bi-image me-1"></i> Change
                  </button>
                  {% endif %}
                  <button class="btn btn-sm btn-light rounded-pill" id="removeCoverImageBtn">
                    <i class="bi bi-trash me-1"></i> Remove
                  </button>
                </div>
                {% endif %}
              </div>
              {% else %}
              <div class="cover-image cover-image-placeholder d-flex align-items-center justify-content-center"
                id="coverImagePlaceholder">
                <div class="text-center">
                  {% if journey.user_id == session.user_id %}
                  {% if premium_access %}
                  <button class="btn btn-light btn-sm rounded-pill" id="addCoverImageBtn">
                    <i class="bi bi-image me-2"></i> Add Cover Image
                  </button>
                  {% else %}
                  <div class="text-muted small mb-2">Cover images are available for premium users</div>
                  <a href="{{ url_for('account.get_profile', active_tab='subscription') }}"
                    class="btn btn-primary btn-sm rounded-pill">
                    <i class="bi bi-star me-1"></i> Upgrade to Premium
                  </a>
                  {% endif %}
                  {% else %}
                  <div class="text-muted">No cover image</div>
                  {% endif %}
                </div>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Journey Map -->
          {% if locations and locations|length > 0 %}
          <div class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="text-uppercase text-muted small fw-bold mb-0">Journey Map</h6>
              <button type="button" class="btn btn-sm btn-outline-primary" onclick="openJourneyMapModal()">
                <i class="bi bi-arrows-fullscreen me-1"></i>Full Map
              </button>
            </div>
            <div class="journey-map-container">
              <div id="journeyMap" style="height: 250px; width: 100%; border-radius: 8px; background: #f8f9fa;"></div>
            </div>
          </div>
          {% endif %}
        </div>



      </div>
    </div>
  </div>

  <!-- Events panel -->
  <div class="col-md-8">
    <div class="card shadow-sm border-0 rounded-3 h-100">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="fs-3 fw-bold mb-0">Events <span class="badge bg-dark rounded-pill" id="eventCount">{{
              events|length }}</span></h2>
          {% if session.get('user_id') and journey.user_id == session.user_id and events %}
          <button class="btn btn-dark rounded-pill px-4 py-2"
                  data-action="create-event"
                  data-journey-id="{{ journey.id }}">
            <i class="bi bi-plus-lg me-2"></i> Add event
          </button>
          {% endif %}
        </div>

        {% if events %}
        <div class="event-content">
          <div class="timeline-container overflow-auto">
            <div class="timeline">
              {% set ns = namespace(displayed_dates={}) %}
              {% for event in events %}
              {% set current_date = event.start_datetime.strftime('%Y-%m-%d') %}

              <div class="timeline-item">
                {% if current_date not in ns.displayed_dates %}
                <div class="timeline-date-marker">
                  <div class="timeline-date">
                    <span class="badge bg-dark rounded-pill py-1 px-3">{{ event.start_datetime.strftime('%b %d, %Y')
                      }}</span>
                  </div>
                </div>
                {% set _ = ns.displayed_dates.update({current_date: true}) %}
                {% endif %}

                <div class="timeline-content-wrapper event-card" data-event-id="{{ event.id }}">
                  <div class="timeline-content card border-0 shadow-sm">
                    <div class="card-body p-0">
                      <!-- Main event content -->
                      <div class="d-flex event-main-content"
                        data-event-url="{{url_for('event.get_event_details', event_id=event.id)}}"
                        onclick="window.location.href=this.dataset.eventUrl"
                        style="cursor: pointer;">
                        {% if event.image %}
                        <div class="timeline-image-container">
                          <img src="{{ url_for('static', filename=get_safe_image_url(event.image, 'event')) }}"
                            class="timeline-image rounded-start" alt="{{ event.title }}">
                        </div>
                        {% else %}
                        <div class="timeline-image-container">
                          <img
                            src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                            class="timeline-image rounded-start" alt="Event placeholder" />
                        </div>
                        {% endif %}

                        <div class="p-3 w-100 position-relative">
                          <h5 class="mb-2 fw-semibold text-truncate">{{ event.title }}</h5>
                          <p class="mb-2 text-muted small"
                            style="min-height: 40px; max-height: 60px; overflow: hidden;">{{ event.description }}
                          </p>

                          <div class="d-flex justify-content-between align-items-center">
                            {% if session.get('user_id') %}
                            <div class="d-flex align-items-center text-primary small event-location-button"
                              style="cursor: pointer;" data-event-id="{{ event.id }}"
                              data-latitude="{{ event.latitude }}" data-longitude="{{ event.longitude }}"
                              data-location-name="{{ event.location_name }}"
                              onclick="event.stopPropagation(); openEventLocationModal(this.dataset.latitude || null, this.dataset.longitude || null, this.dataset.locationName)">
                              <i class="bi bi-geo-alt-fill me-2"></i>
                              <span class="text-truncate">{{ event.location_name }}</span>
                              <i class="bi bi-chevron-down ms-2"></i>
                            </div>
                            {% else %}
                            <!-- Non-logged-in users see location but cannot interact -->
                            <div class="d-flex align-items-center text-muted small">
                              <i class="bi bi-geo-alt-fill me-2"></i>
                              <span class="text-truncate">{{ event.location_name }}</span>
                            </div>
                            {% endif %}
                            <small class="text-muted">{{ event.start_datetime.strftime('%I:%M %p') }}</small>
                          </div>
                        </div>
                      </div>


                    </div>
                  </div>
                </div>
              </div>

              {% endfor %}
            </div>
          </div>
        </div>
        {% else %}
        <div class="event-content d-flex flex-column justify-content-center align-items-center">
          <div class="empty-state text-center">
            <div class="empty-state-icon mb-4">
              <i class="bi bi-calendar-x" style="font-size: 5rem; color: #d1d1d1;"></i>
            </div>
            <h3 class="mb-3 fw-bold" id="noEventsMessage">No events yet</h3>
            <p class="text-muted mb-4">{% if journey.user_id == session.user_id %}Start your journey by adding your
              first
              event!{% else %}This journey doesn't have any events yet.{% endif %}</p>
            {% if session.get('user_id') and journey.user_id == session.user_id %}
            <button class="btn btn-primary rounded-pill px-4 py-2"
                    data-action="create-event"
                    data-journey-id="{{ journey.id }}">
              <i class="bi bi-plus-lg me-2"></i> Add your first event
            </button>
            {% endif %}
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Hidden file input for cover image uploads -->
<input type="file" id="coverImageInput" accept=".png,.jpg,.jpeg,.gif" style="display: none;">

<!-- Full Map Modal -->
<div class="modal fade" id="fullMapModal" tabindex="-1" aria-labelledby="fullMapModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="fullMapModalLabel">Journey Map</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body p-0">
        <div id="fullJourneyMap" style="height: 70vh; width: 100%;"></div>
      </div>
    </div>
  </div>
</div>

<!-- Event Location Modal -->
<div class="modal fade" id="eventLocationModal" tabindex="-1" aria-labelledby="eventLocationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header border-0 pb-0">
        <div class="d-flex align-items-center w-100">
          <div class="d-flex align-items-center flex-grow-1">
            <i class="bi bi-geo-alt-fill text-primary me-2"></i>
            <h5 class="modal-title mb-0" id="eventLocationModalLabel">Event Location</h5>
          </div>
          <div class="d-flex align-items-center gap-2">
            {% if session.get('user_id') and journey.user_id != session.user_id %}
            <button type="button" class="btn btn-dark btn-sm rounded-pill px-3"
                    data-action="toggle-follow"
                    data-journey-id="{{ journey.id }}">
              <i class="bi bi-heart{% if is_following_journey %}-fill{% endif %} me-1"></i>
              <span class="btn-text">{% if is_following_journey %}Following{% else %}Follow{% endif %}</span>
            </button>
            {% endif %}
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
        </div>
      </div>
      <div class="modal-body p-0">
        <div id="fullEventMap" style="height: 50vh; width: 100%;"></div>
      </div>
    </div>
  </div>
</div>








{% endblock %}

